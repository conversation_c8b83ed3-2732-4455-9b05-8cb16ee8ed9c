{"name": "Get All Parameters", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/parameters?name=&type=&component_id=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["parameters"], "query": [{"key": "name", "value": "", "description": "Filter by parameter name"}, {"key": "type", "value": "", "description": "Filter by parameter type"}, {"key": "component_id", "value": "", "description": "Filter by component ID"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}