{"name": "Create Parameter", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"campaign_id\": 1,\n  \"component_id\": 1,\n  \"type\": \"text\",\n  \"value\": \"name\",\n  \"placeholder\": \"<PERSON>\",\n  \"index\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/parameters", "host": ["{{URL}}"], "path": ["parameters"]}}, "response": []}