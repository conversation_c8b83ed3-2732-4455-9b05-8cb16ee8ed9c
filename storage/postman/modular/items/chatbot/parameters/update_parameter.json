{"name": "Update Parameter", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"updated_client_name\",\n  \"type\": \"TEXT\",\n  \"example\": \"<PERSON>\"\n}"}, "url": {"raw": "{{URL}}/parameters/1", "host": ["{{URL}}"], "path": ["parameters", "1"]}}, "response": []}