{"name": "Get All Templates", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/templates?name=&category=&language=&status=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["templates"], "query": [{"key": "name", "value": "", "description": "Filter by template name"}, {"key": "category", "value": "", "description": "Filter by category"}, {"key": "language", "value": "", "description": "Filter by language"}, {"key": "status", "value": "", "description": "Filter by status"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}