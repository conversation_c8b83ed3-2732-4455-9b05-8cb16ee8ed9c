{"name": "Update <PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"quick_reply\"\n}"}, "url": {"raw": "{{URL}}/buttons/1", "host": ["{{URL}}"], "path": ["buttons", "1"]}}, "response": []}