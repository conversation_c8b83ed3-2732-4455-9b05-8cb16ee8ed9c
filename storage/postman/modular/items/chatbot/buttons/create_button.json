{"name": "C<PERSON> <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"text\": \"📞 Falar com Vendedor\",\n  \"type\": \"QUICK_REPLY\",\n  \"internal_type\": \"navigation\",\n  \"internal_data\": \"contact_sales\",\n  \"callback_data\": \"{\\\"action\\\":\\\"contact_sales\\\",\\\"department\\\":\\\"vendas\\\",\\\"priority\\\":\\\"high\\\"}\",\n  \"json\": \"{\\\"style\\\":{\\\"color\\\":\\\"#25D366\\\",\\\"icon\\\":\\\"phone\\\"},\\\"analytics\\\":{\\\"track_clicks\\\":true}}\"\n}"}, "url": {"raw": "{{URL}}/buttons", "host": ["{{URL}}"], "path": ["buttons"]}}, "response": []}