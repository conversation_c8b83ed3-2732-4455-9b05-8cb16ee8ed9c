{"name": "Get All Steps", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/steps?step=&is_input=&input=&order_by=order&order_direction=asc", "host": ["{{URL}}"], "path": ["steps"], "query": [{"key": "step", "value": "", "description": "Filter by step name"}, {"key": "is_input", "value": "", "description": "Filter by input type (true/false)"}, {"key": "input", "value": "", "description": "Filter by input content"}, {"key": "order_by", "value": "order", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}