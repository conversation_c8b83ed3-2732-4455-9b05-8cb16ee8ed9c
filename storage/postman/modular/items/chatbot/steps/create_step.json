{"name": "Create Step", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"flow_id\": 1,\n  \"step\": \"Boas-vindas e Apresentação\",\n  \"type\": \"message\",\n  \"position\": 1,\n  \"next_step\": 2,\n  \"earlier_step\": null,\n  \"is_initial_step\": true,\n  \"is_ending_step\": false,\n  \"is_message\": true,\n  \"is_interactive\": false,\n  \"is_command\": false,\n  \"is_input\": false,\n  \"json\": \"{\\\"message\\\":{\\\"text\\\":\\\"🏗️ Olá! Bem-vindo à nossa loja de materiais de construção! Como posso ajudá-lo hoje?\\\",\\\"type\\\":\\\"text\\\"},\\\"options\\\":{\\\"show_menu\\\":true,\\\"timeout\\\":30000},\\\"analytics\\\":{\\\"track_view\\\":true,\\\"step_name\\\":\\\"welcome\\\"}}\",\n  \"input\": null\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/steps", "host": ["{{URL}}"], "path": ["steps"]}}, "response": []}