{"name": "Update Step", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"step\": \"Updated Welcome Step\",\n  \"is_input\": true,\n  \"input\": \"Please enter your name\"\n}"}, "url": {"raw": "{{URL}}/steps/1", "host": ["{{URL}}"], "path": ["steps", "1"]}}, "response": []}