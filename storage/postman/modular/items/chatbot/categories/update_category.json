{"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Marketing\",\n  \"description\": \"Updated marketing campaigns and promotional messages\",\n  \"color\": \"#10B981\",\n  \"is_active\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/categories/1", "host": ["{{URL}}"], "path": ["categories", "1"]}}, "response": []}