{"name": "Get All Campaigns", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaigns?name=&template_id=&phone_number_id=&sent_at_greater_than=&sent_at_lower_than=&scheduled_at_greater_than=&scheduled_at_lower_than=&is_sending=&is_sent=&is_scheduled=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["campaigns"], "query": [{"key": "name", "value": "", "description": "Filter by campaign name"}, {"key": "template_id", "value": "", "description": "Filter by template ID"}, {"key": "phone_number_id", "value": "", "description": "Filter by phone number ID"}, {"key": "sent_at_greater_than", "value": "", "description": "Filter sent after date"}, {"key": "sent_at_lower_than", "value": "", "description": "Filter sent before date"}, {"key": "scheduled_at_greater_than", "value": "", "description": "Filter scheduled after date"}, {"key": "scheduled_at_lower_than", "value": "", "description": "Filter scheduled before date"}, {"key": "is_sending", "value": "", "description": "Filter by sending status"}, {"key": "is_sent", "value": "", "description": "Filter by sent status"}, {"key": "is_scheduled", "value": "", "description": "Filter by scheduled status"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}