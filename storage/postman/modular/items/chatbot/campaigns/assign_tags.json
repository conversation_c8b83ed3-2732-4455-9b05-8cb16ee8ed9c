{"name": "Assign <PERSON>s", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"tags\": [\"marketing\", \"promotion\", \"new-product\", \"seasonal\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/campaign/1/tags", "host": ["{{URL}}"], "path": ["campaign", "1", "tags"]}}, "response": []}