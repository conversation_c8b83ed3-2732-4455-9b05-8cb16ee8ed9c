{"name": "Create WhatsApp Message", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"message_id\": 1,\n  \"whatsapp_message_id\": \"wamid.HBgMNTUxMTk5OTg4Nzc2NhUCABIYFjNBMDJCMDlBNjBCMzU3Q0RBMTU2RkEA\",\n  \"message_status\": \"sent\",\n  \"wa_id\": \"5511999887766\",\n  \"input_phone\": \"+5511999887766\",\n  \"messaging_product\": \"whatsapp\",\n  \"json\": \"{\\\"delivery_status\\\":{\\\"sent_at\\\":\\\"2024-11-27 14:30:00\\\",\\\"delivered_at\\\":null,\\\"read_at\\\":null},\\\"webhook_data\\\":{\\\"message_type\\\":\\\"template\\\",\\\"template_name\\\":\\\"promocao_construcao\\\"},\\\"tracking\\\":{\\\"campaign_id\\\":1,\\\"client_id\\\":1}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp_messages", "host": ["{{URL}}"], "path": ["whatsapp_messages"]}}, "response": []}