{"name": "Trigger Calculation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"calculation_type\": \"campaign_metrics\",\n  \"entity_ids\": [1, 2, 3],\n  \"force_recalculation\": false,\n  \"include_historical\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/analytics/trigger-calculation", "host": ["{{URL}}"], "path": ["analytics", "trigger-calculation"]}}, "response": []}