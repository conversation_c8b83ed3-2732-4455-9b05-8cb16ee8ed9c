{"name": "Get Campaign Analytics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/analytics/campaign/1?include_engagement=true&include_delivery=true", "host": ["{{URL}}"], "path": ["analytics", "campaign", "1"], "query": [{"key": "include_engagement", "value": "true", "description": "Include engagement metrics"}, {"key": "include_delivery", "value": "true", "description": "Include delivery metrics"}]}}, "response": []}