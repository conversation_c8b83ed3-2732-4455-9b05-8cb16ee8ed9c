{"name": "Record Bulk Engagement Events", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"events\": [\n    {\n      \"message_id\": 1,\n      \"event_type\": \"delivered\",\n      \"timestamp\": \"2024-01-15T10:00:00Z\",\n      \"client_id\": 1\n    },\n    {\n      \"message_id\": 1,\n      \"event_type\": \"read\",\n      \"timestamp\": \"2024-01-15T10:05:00Z\",\n      \"client_id\": 1\n    },\n    {\n      \"message_id\": 2,\n      \"event_type\": \"click\",\n      \"event_data\": {\n        \"button_id\": \"btn_2\"\n      },\n      \"timestamp\": \"2024-01-15T10:10:00Z\",\n      \"client_id\": 2\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/analytics/engagement/bulk", "host": ["{{URL}}"], "path": ["analytics", "engagement", "bulk"]}}, "response": []}