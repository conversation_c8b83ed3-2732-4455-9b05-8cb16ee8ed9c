{"name": "Record Engagement Event", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"message_id\": 1,\n  \"event_type\": \"click\",\n  \"event_data\": {\n    \"button_id\": \"btn_1\",\n    \"button_text\": \"Learn More\",\n    \"timestamp\": \"2024-01-15T10:30:00Z\"\n  },\n  \"client_id\": 1,\n  \"campaign_id\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/analytics/engagement/record", "host": ["{{URL}}"], "path": ["analytics", "engagement", "record"]}}, "response": []}