{"name": "Get Dashboard", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/analytics/dashboard?period=30d&timezone=America/Sao_Paulo", "host": ["{{URL}}"], "path": ["analytics", "dashboard"], "query": [{"key": "period", "value": "30d", "description": "Time period (7d, 30d, 90d, 1y)"}, {"key": "timezone", "value": "America/Sao_Paulo", "description": "Timezone for date calculations"}]}}, "response": []}