{"name": "Get Performance Comparison", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"campaign_ids\": [1, 2],\n  \"comparison_type\": \"side_by_side\",\n  \"metrics\": [\"delivery_rate\", \"engagement_rate\", \"conversion_rate\"],\n  \"date_range\": {\n    \"start_date\": \"2024-01-01\",\n    \"end_date\": \"2024-01-31\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/analytics/campaigns/compare", "host": ["{{URL}}"], "path": ["analytics", "campaigns", "compare"]}}, "response": []}