{"name": "Get All Components", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/components?name=&template_id=&format=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["components"], "query": [{"key": "name", "value": "", "description": "Filter by component name"}, {"key": "template_id", "value": "", "description": "Filter by template ID"}, {"key": "format", "value": "", "description": "Filter by format (TEXT, IMAGE, VIDEO, DOCUMENT)"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}