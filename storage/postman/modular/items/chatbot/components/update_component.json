{"name": "Update Component", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Welcome Component\"\n}"}, "url": {"raw": "{{URL}}/components/1", "host": ["{{URL}}"], "path": ["components", "1"]}}, "response": []}