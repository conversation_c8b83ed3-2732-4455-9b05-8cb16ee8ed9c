{"name": "Get Most Used Tags", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/tags/most-used?limit=20", "host": ["{{URL}}"], "path": ["tags", "most-used"], "query": [{"key": "limit", "value": "20", "description": "Limit number of most used tags"}]}}, "response": []}