{"name": "Get Tag Suggestions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/tags/suggestions?query=mark&limit=10", "host": ["{{URL}}"], "path": ["tags", "suggestions"], "query": [{"key": "query", "value": "mark", "description": "Search query for tag suggestions"}, {"key": "limit", "value": "10", "description": "Limit number of suggestions"}]}}, "response": []}