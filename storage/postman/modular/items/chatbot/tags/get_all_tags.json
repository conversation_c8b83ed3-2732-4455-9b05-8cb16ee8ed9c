{"name": "Get All Tags", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/tags?search=&limit=50", "host": ["{{URL}}"], "path": ["tags"], "query": [{"key": "search", "value": "", "description": "Search tags by name"}, {"key": "limit", "value": "50", "description": "Limit number of results"}]}}, "response": []}