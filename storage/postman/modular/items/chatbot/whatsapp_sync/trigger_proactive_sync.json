{"name": "<PERSON><PERSON> Proactive Sync", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"sync_type\": \"full\",\n  \"entity_types\": [\"message\", \"campaign\"],\n  \"priority\": \"high\",\n  \"batch_size\": 50,\n  \"delay_between_batches\": 1000\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/sync/trigger-proactive", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "trigger-proactive"]}}, "response": []}