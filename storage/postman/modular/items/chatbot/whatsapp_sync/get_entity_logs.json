{"name": "Get Entity Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/sync/entity-logs?entity_type=message&entity_id=1&include_details=true", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "entity-logs"], "query": [{"key": "entity_type", "value": "message", "description": "Entity type (message, campaign)"}, {"key": "entity_id", "value": "1", "description": "Entity ID"}, {"key": "include_details", "value": "true", "description": "Include detailed sync information"}]}}, "response": []}