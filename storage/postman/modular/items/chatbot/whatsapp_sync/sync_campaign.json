{"name": "Sync Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"sync_all_messages\": true,\n  \"force_sync\": false,\n  \"include_analytics\": true,\n  \"batch_size\": 100\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/sync/campaign/1", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "campaign", "1"]}}, "response": []}