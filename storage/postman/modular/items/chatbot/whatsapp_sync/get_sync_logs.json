{"name": "Get Sync Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/sync/logs?entity_type=&entity_id=&status=&date_from=&date_to=&page=1&per_page=50", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "logs"], "query": [{"key": "entity_type", "value": "", "description": "Filter by entity type (message, campaign)"}, {"key": "entity_id", "value": "", "description": "Filter by entity ID"}, {"key": "status", "value": "", "description": "Filter by sync status"}, {"key": "date_from", "value": "", "description": "Filter from date (YYYY-MM-DD)"}, {"key": "date_to", "value": "", "description": "Filter to date (YYYY-MM-DD)"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "per_page", "value": "50", "description": "Items per page"}]}}, "response": []}