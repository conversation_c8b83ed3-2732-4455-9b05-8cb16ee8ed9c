{"name": "Get Sync Trends", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/sync/trends?period=7d&group_by=day&include_success_rate=true", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "trends"], "query": [{"key": "period", "value": "7d", "description": "Time period (1d, 7d, 30d, 90d)"}, {"key": "group_by", "value": "day", "description": "Group by (hour, day, week)"}, {"key": "include_success_rate", "value": "true", "description": "Include success rate metrics"}]}}, "response": []}