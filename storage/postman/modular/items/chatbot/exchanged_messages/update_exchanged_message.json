{"name": "Update Exchanged Message", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"phone_number_id\": 1,\n  \"client_id\": 1,\n  \"conversation_id\": 1,\n  \"direction\": \"outbound\",\n  \"external_message_id\": \"wamid.HBgNNTU3OTgxMTY2NjQwFQIAERgSNzM4QjY5RjY5QjY5RjY5QjY5AA==\",\n  \"message_text\": \"Thank you for contacting us. How can we help you today?\",\n  \"message_type\": \"text\",\n  \"message_data\": \"{\\\"text\\\":{\\\"body\\\":\\\"Thank you for contacting us. How can we help you today?\\\"},\\\"to\\\":\\\"557981166640\\\",\\\"id\\\":\\\"wamid.HBgNNTU3OTgxMTY2NjQwFQIAERgSNzM4QjY5RjY5QjY5RjY5QjY5AA==\\\",\\\"timestamp\\\":\\\"1757554504\\\",\\\"type\\\":\\\"text\\\"}\",\n  \"webhook_data\": \"{\\\"entry\\\":[{\\\"id\\\":\\\"123456789\\\",\\\"changes\\\":[{\\\"value\\\":{\\\"messaging_product\\\":\\\"whatsapp\\\",\\\"metadata\\\":{\\\"display_phone_number\\\":\\\"15550123456\\\",\\\"phone_number_id\\\":\\\"123456789\\\"},\\\"statuses\\\":[{\\\"id\\\":\\\"wamid.HBgNNTU3OTgxMTY2NjQwFQIAERgSNzM4QjY5RjY5QjY5RjY5QjY5AA==\\\",\\\"status\\\":\\\"sent\\\",\\\"timestamp\\\":\\\"1757554504\\\",\\\"recipient_id\\\":\\\"557981166640\\\"}]},\\\"field\\\":\\\"messages\\\"}]}]}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/exchanged_messages/{{exchanged_message_id}}", "host": ["{{URL}}"], "path": ["exchanged_messages", "{{exchanged_message_id}}"]}}, "response": []}