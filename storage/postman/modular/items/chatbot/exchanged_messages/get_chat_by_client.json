{"name": "Get Chat by Client", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/exchanged_messages/chat-by-client/{{CLIENT_ID}}?limit=50", "host": ["{{URL}}"], "path": ["exchanged_messages", "chat-by-client", "{{CLIENT_ID}}"], "query": [{"key": "limit", "value": "50", "description": "Number of messages to retrieve (max: 100, default: 50)"}]}}, "response": [{"name": "Success - Chat History Retrieved", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/exchanged_messages/chat-by-client/123?limit=50", "host": ["{{URL}}"], "path": ["exchanged_messages", "chat-by-client", "123"], "query": [{"key": "limit", "value": "50"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"message\": \"Chat history retrieved successfully\",\n    \"data\": [\n        {\n            \"id\": 1,\n            \"organization_id\": 1,\n            \"client_id\": 123,\n            \"phone_number_id\": 1,\n            \"conversation_id\": null,\n            \"message_id\": null,\n            \"whatsapp_message_id\": null,\n            \"external_message_id\": \"wamid.HBgNNTU3OTgxMTY2NjQwFQIAERgSMDFFODFBQzJFRkUyRjc1N0VFAA==\",\n            \"inbound\": true,\n            \"outbound\": false,\n            \"message\": \"<PERSON><PERSON><PERSON>, gostaria de saber mais sobre os produtos\",\n            \"sent_at\": \"2025-09-11 10:00:00\",\n            \"created_at\": \"2025-09-11 10:00:00\",\n            \"updated_at\": \"2025-09-11 10:00:00\"\n        },\n        {\n            \"id\": 2,\n            \"organization_id\": 1,\n            \"client_id\": 123,\n            \"phone_number_id\": 1,\n            \"conversation_id\": null,\n            \"message_id\": 456,\n            \"whatsapp_message_id\": 789,\n            \"external_message_id\": null,\n            \"inbound\": false,\n            \"outbound\": true,\n            \"message\": \"Ol<PERSON>! Claro, posso te ajudar. Temos diversos produtos disponíveis.\",\n            \"sent_at\": \"2025-09-11 10:05:00\",\n            \"created_at\": \"2025-09-11 10:05:00\",\n            \"updated_at\": \"2025-09-11 10:05:00\"\n        },\n        {\n            \"id\": 3,\n            \"organization_id\": 1,\n            \"client_id\": 123,\n            \"phone_number_id\": 1,\n            \"conversation_id\": null,\n            \"message_id\": null,\n            \"whatsapp_message_id\": null,\n            \"external_message_id\": \"wamid.HBgNNTU3OTgxMTY2NjQwFQIAERgSMDFFODFBQzJFRkUyRjc1N0VFBB==\",\n            \"inbound\": true,\n            \"outbound\": false,\n            \"message\": \"Perfeito! Quais são os preços?\",\n            \"sent_at\": \"2025-09-11 10:10:00\",\n            \"created_at\": \"2025-09-11 10:10:00\",\n            \"updated_at\": \"2025-09-11 10:10:00\"\n        }\n    ],\n    \"errors\": null,\n    \"pagination\": {\n        \"count\": 3,\n        \"client_id\": 123,\n        \"organization_id\": 1,\n        \"limit\": 50\n    }\n}"}, {"name": "Success - Empty Chat", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/exchanged_messages/chat-by-client/456?limit=50", "host": ["{{URL}}"], "path": ["exchanged_messages", "chat-by-client", "456"], "query": [{"key": "limit", "value": "50"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"message\": \"Chat history retrieved successfully\",\n    \"data\": [],\n    \"errors\": null,\n    \"pagination\": {\n        \"count\": 0,\n        \"client_id\": 456,\n        \"organization_id\": 1,\n        \"limit\": 50\n    }\n}"}, {"name": "Error - Client Not Found", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/exchanged_messages/chat-by-client/999?limit=50", "host": ["{{URL}}"], "path": ["exchanged_messages", "chat-by-client", "999"], "query": [{"key": "limit", "value": "50"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"message\": \"Client not found\",\n    \"data\": null,\n    \"errors\": null,\n    \"pagination\": null\n}"}, {"name": "Error - Client from Different Organization", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/exchanged_messages/chat-by-client/789?limit=50", "host": ["{{URL}}"], "path": ["exchanged_messages", "chat-by-client", "789"], "query": [{"key": "limit", "value": "50"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"message\": \"Client does not belong to organization\",\n    \"data\": null,\n    \"errors\": null,\n    \"pagination\": null\n}"}, {"name": "Error - Unauthorized", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{URL}}/exchanged_messages/chat-by-client/123?limit=50", "host": ["{{URL}}"], "path": ["exchanged_messages", "chat-by-client", "123"], "query": [{"key": "limit", "value": "50"}]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"message\": \"Una<PERSON><PERSON><PERSON><PERSON>.\"\n}"}]}