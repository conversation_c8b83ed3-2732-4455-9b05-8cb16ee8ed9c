{"name": "Get All Exchanged Messages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/exchanged_messages?phone_number_id=&client_id=&conversation_id=&direction=&external_message_id=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["exchanged_messages"], "query": [{"key": "phone_number_id", "value": "", "description": "Filter by phone number ID"}, {"key": "client_id", "value": "", "description": "Filter by client ID"}, {"key": "conversation_id", "value": "", "description": "Filter by conversation ID"}, {"key": "direction", "value": "", "description": "Filter by direction (inbound/outbound)"}, {"key": "external_message_id", "value": "", "description": "Filter by external message ID"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}