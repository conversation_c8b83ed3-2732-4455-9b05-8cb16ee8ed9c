{"name": "Get All Messages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/messages?campaign_id=&client_id=&phone_number_id=&sent_at_greater_than=&sent_at_lower_than=&is_sent=&is_delivered=&is_read=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["messages"], "query": [{"key": "campaign_id", "value": "", "description": "Filter by campaign ID"}, {"key": "client_id", "value": "", "description": "Filter by client ID"}, {"key": "phone_number_id", "value": "", "description": "Filter by phone number ID"}, {"key": "sent_at_greater_than", "value": "", "description": "Filter sent after date"}, {"key": "sent_at_lower_than", "value": "", "description": "Filter sent before date"}, {"key": "is_sent", "value": "", "description": "Filter by sent status"}, {"key": "is_delivered", "value": "", "description": "Filter by delivered status"}, {"key": "is_read", "value": "", "description": "Filter by read status"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}