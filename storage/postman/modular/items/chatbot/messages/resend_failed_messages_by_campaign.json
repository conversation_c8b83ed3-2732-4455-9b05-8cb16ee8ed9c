{"name": "Resend Failed Messages by Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"max_retry_attempts\": 3,\n  \"delay_between_retries\": 300,\n  \"filter_by_error_type\": \"timeout\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/campaign/1/messages/resend-failed", "host": ["{{URL}}"], "path": ["campaign", "1", "messages", "resend-failed"]}}, "response": []}