{"name": "Get Messages by Campaign", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaign/1/messages?status=&is_sent=&is_delivered=&is_read=&page=1&per_page=50", "host": ["{{URL}}"], "path": ["campaign", "1", "messages"], "query": [{"key": "status", "value": "", "description": "Filter by message status"}, {"key": "is_sent", "value": "", "description": "Filter by sent status"}, {"key": "is_delivered", "value": "", "description": "Filter by delivered status"}, {"key": "is_read", "value": "", "description": "Filter by read status"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "per_page", "value": "50", "description": "Items per page"}]}}, "response": []}