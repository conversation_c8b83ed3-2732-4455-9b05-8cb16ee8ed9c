{"name": "Get Failed Messages by Campaign", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaign/1/messages/failed?page=1&per_page=50", "host": ["{{URL}}"], "path": ["campaign", "1", "messages", "failed"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "per_page", "value": "50", "description": "Items per page"}]}}, "response": []}