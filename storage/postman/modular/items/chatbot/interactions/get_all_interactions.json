{"name": "Get All Interactions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/interactions?conversation_id=&step_id=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["interactions"], "query": [{"key": "conversation_id", "value": "", "description": "Filter by conversation ID"}, {"key": "step_id", "value": "", "description": "Filter by step ID"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}