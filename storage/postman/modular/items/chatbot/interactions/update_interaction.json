{"name": "Update Interaction", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"conversation_id\": 1,\n  \"step_id\": 2,\n  \"input\": \"Updated user response\"\n}"}, "url": {"raw": "{{URL}}/interactions/1", "host": ["{{URL}}"], "path": ["interactions", "1"]}}, "response": []}