{"name": "Get All Webhook Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp-webhook-logs?page=1&per_page=50&sort=created_at&order=desc", "host": ["{{URL}}"], "path": ["whatsapp-webhook-logs"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "per_page", "value": "50", "description": "Number of logs per page (max 100)"}, {"key": "sort", "value": "created_at", "description": "Sort field: created_at, event_type, processing_status"}, {"key": "order", "value": "desc", "description": "Sort order: asc, desc"}, {"key": "organization_id", "value": "", "description": "Filter by organization ID", "disabled": true}, {"key": "phone_number_id", "value": "", "description": "Filter by phone number ID", "disabled": true}, {"key": "event_type", "value": "", "description": "Filter by event type: messages, verification, security, other", "disabled": true}, {"key": "processing_status", "value": "", "description": "Filter by status: pending, success, failed", "disabled": true}, {"key": "date_from", "value": "", "description": "Filter from date (Y-m-d H:i:s)", "disabled": true}, {"key": "date_to", "value": "", "description": "Filter to date (Y-m-d H:i:s)", "disabled": true}]}, "description": "Retrieve all WhatsApp webhook logs with comprehensive filtering and pagination options. Supports filtering by organization, phone number, event type, processing status, and date range. Essential for monitoring webhook processing, debugging issues, and analyzing system performance."}, "response": []}