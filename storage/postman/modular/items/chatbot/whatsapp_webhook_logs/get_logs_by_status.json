{"name": "Get Logs by Processing Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp-webhook-logs/status/failed?page=1&per_page=50&date_from=2024-01-01&include_error_details=true", "host": ["{{URL}}"], "path": ["whatsapp-webhook-logs", "status", "failed"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "per_page", "value": "50", "description": "Number of logs per page (max 100)"}, {"key": "date_from", "value": "2024-01-01", "description": "Filter from date (Y-m-d)"}, {"key": "date_to", "value": "", "description": "Filter to date (Y-m-d)", "disabled": true}, {"key": "organization_id", "value": "", "description": "Filter by organization ID", "disabled": true}, {"key": "phone_number_id", "value": "", "description": "Filter by phone number ID", "disabled": true}, {"key": "event_type", "value": "", "description": "Filter by event type: messages, verification, security, other", "disabled": true}, {"key": "include_error_details", "value": "true", "description": "Include detailed error messages and stack traces"}], "variable": [{"key": "status", "value": "failed", "description": "Processing status: pending, success, failed"}]}, "description": "Retrieve WhatsApp webhook logs filtered by processing status. Essential for monitoring failed webhooks, identifying processing issues, and tracking system reliability. Supports detailed error information for debugging failed webhook events."}, "response": []}