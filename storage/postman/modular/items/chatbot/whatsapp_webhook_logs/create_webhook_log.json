{"name": "Create Webhook Log", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"organization_id\": 1,\n  \"phone_number_id\": \"***************\",\n  \"event_type\": \"messages\",\n  \"webhook_payload\": {\n    \"object\": \"whatsapp_business_account\",\n    \"entry\": [\n      {\n        \"id\": \"business_account_id\",\n        \"changes\": [\n          {\n            \"value\": {\n              \"messaging_product\": \"whatsapp\",\n              \"metadata\": {\n                \"display_phone_number\": \"+55 11 99999-9999\",\n                \"phone_number_id\": \"***************\"\n              },\n              \"messages\": [\n                {\n                  \"from\": \"*************\",\n                  \"id\": \"wamid.HBgLNTUxMTk5OTk5OTk5ORUCABIYIDdGNjA4NzQwNzRBNzQ4NzI4RjE2NzI4RjE2NzI4RjE2\",\n                  \"timestamp\": \"**********\",\n                  \"text\": {\n                    \"body\": \"Olá, preciso de ajuda!\"\n                  },\n                  \"type\": \"text\"\n                }\n              ]\n            },\n            \"field\": \"messages\"\n          }\n        ]\n      }\n    ]\n  },\n  \"processing_status\": \"pending\",\n  \"error_message\": null\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp-webhook-logs", "host": ["{{URL}}"], "path": ["whatsapp-webhook-logs"]}, "description": "Create a new WhatsApp webhook log entry. Used internally by the system to log incoming webhook events. Includes organization ID, phone number ID, event type, full webhook payload, and initial processing status."}, "response": []}