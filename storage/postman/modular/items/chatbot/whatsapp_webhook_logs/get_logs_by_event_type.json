{"name": "Get Logs by Event Type", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp-webhook-logs/event-type/messages?page=1&per_page=50&date_from=2024-01-01&date_to=2024-01-31", "host": ["{{URL}}"], "path": ["whatsapp-webhook-logs", "event-type", "messages"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "per_page", "value": "50", "description": "Number of logs per page (max 100)"}, {"key": "date_from", "value": "2024-01-01", "description": "Filter from date (Y-m-d)"}, {"key": "date_to", "value": "2024-01-31", "description": "Filter to date (Y-m-d)"}, {"key": "organization_id", "value": "", "description": "Filter by organization ID", "disabled": true}, {"key": "phone_number_id", "value": "", "description": "Filter by phone number ID", "disabled": true}, {"key": "processing_status", "value": "", "description": "Filter by status: pending, success, failed", "disabled": true}], "variable": [{"key": "eventType", "value": "messages", "description": "Event type: messages, verification, security, other"}]}, "description": "Retrieve WhatsApp webhook logs filtered by specific event type. Supports filtering by messages, verification, security, or other event types. Essential for analyzing specific types of webhook events, debugging event-specific issues, and monitoring event type performance."}, "response": []}