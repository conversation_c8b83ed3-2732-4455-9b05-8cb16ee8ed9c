{"name": "Get Statistics Summary", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp-webhook-logs/statistics/summary?period=7d&organization_id=1&include_trends=true&group_by=event_type", "host": ["{{URL}}"], "path": ["whatsapp-webhook-logs", "statistics", "summary"], "query": [{"key": "period", "value": "7d", "description": "Time period: 1h, 6h, 24h, 7d, 30d, 90d (default: 24h)"}, {"key": "organization_id", "value": "1", "description": "Filter by organization ID"}, {"key": "phone_number_id", "value": "", "description": "Filter by phone number ID", "disabled": true}, {"key": "include_trends", "value": "true", "description": "Include trend analysis and comparisons"}, {"key": "group_by", "value": "event_type", "description": "Group statistics by: event_type, processing_status, hour, day"}, {"key": "include_performance", "value": "", "description": "Include performance metrics (response times, throughput)", "disabled": true}, {"key": "include_errors", "value": "", "description": "Include detailed error analysis", "disabled": true}]}, "description": "Get comprehensive statistics summary for WhatsApp webhook logs. Provides aggregated metrics including total events, success/failure rates, event type distribution, processing performance, and trend analysis. Essential for monitoring system health, identifying patterns, and generating reports."}, "response": []}