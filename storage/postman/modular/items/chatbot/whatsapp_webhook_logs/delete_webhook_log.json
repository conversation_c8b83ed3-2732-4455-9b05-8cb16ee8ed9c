{"name": "Delete Webhook Log", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp-webhook-logs/1", "host": ["{{URL}}"], "path": ["whatsapp-webhook-logs", "1"], "variable": [{"key": "id", "value": "1", "description": "Webhook log ID"}]}, "description": "Delete a specific WhatsApp webhook log entry. Use with caution as this permanently removes the log record. Typically used for cleanup of old logs or removing test data. Consider archiving instead of deleting for audit trail purposes."}, "response": []}