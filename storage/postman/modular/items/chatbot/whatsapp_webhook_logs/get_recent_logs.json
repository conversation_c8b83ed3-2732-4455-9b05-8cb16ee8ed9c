{"name": "Get Recent Webhook Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp-webhook-logs/recent/list?hours=24&limit=100&organization_id=1", "host": ["{{URL}}"], "path": ["whatsapp-webhook-logs", "recent", "list"], "query": [{"key": "hours", "value": "24", "description": "Number of hours to look back (default: 24)"}, {"key": "limit", "value": "100", "description": "Maximum number of logs to return (default: 50, max: 500)"}, {"key": "organization_id", "value": "1", "description": "Filter by organization ID"}, {"key": "phone_number_id", "value": "", "description": "Filter by phone number ID", "disabled": true}, {"key": "processing_status", "value": "", "description": "Filter by status: pending, success, failed", "disabled": true}, {"key": "event_type", "value": "", "description": "Filter by event type: messages, verification, security, other", "disabled": true}]}, "description": "Retrieve recent WhatsApp webhook logs within a specified time window. Optimized for real-time monitoring and quick access to latest webhook events. Essential for monitoring system health, debugging recent issues, and tracking webhook processing performance."}, "response": []}