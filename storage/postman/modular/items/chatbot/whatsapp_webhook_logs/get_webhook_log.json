{"name": "Get Webhook Log", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp-webhook-logs/1", "host": ["{{URL}}"], "path": ["whatsapp-webhook-logs", "1"], "variable": [{"key": "id", "value": "1", "description": "Webhook log ID"}]}, "description": "Retrieve a specific WhatsApp webhook log by ID. Returns detailed information including webhook payload, processing status, error messages, and timestamps. Essential for debugging specific webhook events and analyzing processing details."}, "response": []}