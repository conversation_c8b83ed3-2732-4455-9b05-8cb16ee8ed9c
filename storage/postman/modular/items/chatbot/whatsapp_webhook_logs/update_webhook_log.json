{"name": "Update Webhook Log", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"processing_status\": \"success\",\n  \"processed_at\": \"2024-01-15 10:30:45\",\n  \"error_message\": null\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp-webhook-logs/1", "host": ["{{URL}}"], "path": ["whatsapp-webhook-logs", "1"], "variable": [{"key": "id", "value": "1", "description": "Webhook log ID"}]}, "description": "Update an existing WhatsApp webhook log entry. Typically used to update processing status, add error messages, or set processed timestamp. Essential for tracking webhook processing lifecycle and debugging failed events."}, "response": []}