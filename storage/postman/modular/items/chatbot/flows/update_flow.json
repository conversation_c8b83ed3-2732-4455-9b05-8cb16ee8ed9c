{"name": "Update Flow", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Welcome Flow\"\n}"}, "url": {"raw": "{{URL}}/flows/1", "host": ["{{URL}}"], "path": ["flows", "1"]}}, "response": []}