{"name": "Save Full Flow", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"flow\": {\n    \"name\": \"Complete Flow\",\n    \"steps\": [\n      {\n        \"step\": \"Welcome\",\n        \"is_input\": false,\n        \"components\": [\n          {\n            \"name\": \"Welcome Message\",\n            \"type\": \"BODY\",\n            \"text\": \"Welcome to our service!\"\n          }\n        ]\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{URL}}/flow/save", "host": ["{{URL}}"], "path": ["flow", "save"]}}, "response": []}