{"name": "Create Flow", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Fluxo de Boas-Vindas WhatsApp\",\n  \"description\": \"Fluxo automatizado de boas-vindas para novos clientes no WhatsApp. Inclui apresentação da empresa, menu de opções, coleta de dados básicos e direcionamento para atendimento humano quando necessário.\",\n  \"steps_count\": 5,\n  \"json\": \"{\\\"nodes\\\":[{\\\"id\\\":\\\"start\\\",\\\"type\\\":\\\"welcome\\\",\\\"data\\\":{\\\"message\\\":\\\"Olá! Bem-vindo à nossa empresa!\\\"}},{\\\"id\\\":\\\"menu\\\",\\\"type\\\":\\\"menu\\\",\\\"data\\\":{\\\"options\\\":[\\\"Produtos\\\",\\\"Serviços\\\",\\\"Suporte\\\"]}}],\\\"edges\\\":[{\\\"source\\\":\\\"start\\\",\\\"target\\\":\\\"menu\\\"}]}\",\n  \"is_default_flow\": false\n}"}, "url": {"raw": "{{URL}}/flows", "host": ["{{URL}}"], "path": ["flows"]}}, "response": []}