{"name": "Update Conversation", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"phone_number_id\": 1,\n  \"client_id\": 2,\n  \"flow_id\": 1,\n  \"is_active\": false\n}"}, "url": {"raw": "{{URL}}/conversations/1", "host": ["{{URL}}"], "path": ["conversations", "1"]}}, "response": []}