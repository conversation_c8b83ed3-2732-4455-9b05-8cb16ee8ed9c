{"name": "Create Conversation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": 1,\n  \"flow_id\": 1,\n  \"phone_number_id\": 1,\n  \"current_step_id\": 1,\n  \"json\": \"{\\\"context\\\":{\\\"customer_name\\\":\\\"<PERSON>\\\",\\\"last_interaction\\\":\\\"2024-11-27 14:30:00\\\",\\\"preferences\\\":{\\\"language\\\":\\\"pt_BR\\\",\\\"communication_style\\\":\\\"formal\\\"}},\\\"session_data\\\":{\\\"started_at\\\":\\\"2024-11-27 14:25:00\\\",\\\"source\\\":\\\"whatsapp\\\",\\\"device\\\":\\\"mobile\\\"}}\",\n  \"is_finished\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/conversations", "host": ["{{URL}}"], "path": ["conversations"]}}, "response": []}