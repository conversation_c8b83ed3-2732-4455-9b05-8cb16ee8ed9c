{"name": "Get All Conversations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/conversations?phone_number_id=&client_id=&flow_id=&is_active=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["conversations"], "query": [{"key": "phone_number_id", "value": "", "description": "Filter by phone number ID"}, {"key": "client_id", "value": "", "description": "Filter by client ID"}, {"key": "flow_id", "value": "", "description": "Filter by flow ID"}, {"key": "is_active", "value": "", "description": "Filter by active status"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}