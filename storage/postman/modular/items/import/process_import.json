{"name": "Process Import", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "skip_header", "value": "1", "description": "Whether to skip the first row (header) when processing (1 = true, 0 = false)", "type": "text"}, {"key": "reprocess", "value": "0", "description": "Whether to reprocess an already processed import (1 = true, 0 = false)", "type": "text"}]}, "url": {"raw": "{{URL}}/import/{{import_id}}/process", "host": ["{{URL}}"], "path": ["import", "{{import_id}}", "process"], "variable": [{"key": "import_id", "value": "1", "description": "The ID of the import to process"}]}}, "response": []}