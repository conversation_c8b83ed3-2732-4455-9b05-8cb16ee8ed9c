{"name": "Create Import", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "description": "Excel/CSV file to import (max 8MB, extensions: jpg,jpeg,csv,xls,xlsx,gif,png,pdf,doc,docx,txt,txtx,text)", "type": "file", "src": []}, {"key": "model", "value": "products", "description": "Model to import data into (products, brands, groups, clients, projects, budgets, stocks, stock_entries, stock_exits)", "type": "text"}]}, "url": {"raw": "{{URL}}/imports", "host": ["{{URL}}"], "path": ["imports"]}}, "response": []}