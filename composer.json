{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "doctrine/dbal": "^3.9", "google/cloud-vision": "^2.0", "guzzlehttp/guzzle": "^7.2", "irazasyed/telegram-bot-sdk": "^3.14", "laravel/framework": "^11.0", "laravel/reverb": "^1.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.8", "maatwebsite/excel": "^3.1", "mohammad-fouladgar/eloquent-builder": "^5.0", "nunomaduro/collision": "^8.8", "nunomaduro/termwind": "^2.3", "phpunit/phpunit": "^11.5", "thiagoalessio/tesseract_ocr": "^2.13"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}, "platform": {"php": "8.3.6"}}, "minimum-stability": "stable", "prefer-stable": true}