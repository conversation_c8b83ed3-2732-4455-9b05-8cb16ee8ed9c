<?php

namespace Tests\Feature\Services\Meta\WhatsApp\ChatBot\Processors;

use Tests\TestCase;
use App\Services\Meta\WhatsApp\ChatBot\Processors\InteractiveStepProcessor;
use App\Domains\ChatBot\Step;
use App\Domains\ChatBot\Component;
use App\Domains\ChatBot\Button;
use App\Domains\ChatBot\Conversation;
use App\Domains\ChatBot\Interaction;
use App\Enums\StepType;

class InteractiveStepProcessorTest extends TestCase
{
    /** @test */
    public function it_can_be_instantiated()
    {
        $processor = app()->make(InteractiveStepProcessor::class);
        $this->assertInstanceOf(InteractiveStepProcessor::class, $processor);
    }

    /** @test */
    public function it_handles_step_with_component_and_buttons()
    {
        $processor = app()->make(InteractiveStepProcessor::class);

        // Create buttons
        $button1 = new Button(
            id: 1,
            organization_id: 1,
            text: 'Option 1',
            type: 'reply',
            internal_type: null,
            internal_data: null,
            callback_data: '{"button_id":"btn1"}',
            json: null
        );

        $button2 = new Button(
            id: 2,
            organization_id: 1,
            text: 'Option 2',
            type: 'reply',
            internal_type: null,
            internal_data: null,
            callback_data: '{"button_id":"btn2"}',
            json: null
        );

        // Create component with buttons
        $component = new Component(
            id: 1,
            organization_id: 1,
            step_id: 1,
            template_id: null,
            name: 'Test Component',
            type: 'interactive',
            sub_type: null,
            index: 1,
            text: 'Choose an option:',
            format: null,
            json: null,
            created_at: null,
            updated_at: null,
            step: null,
            template: null,
            buttons: [$button1, $button2],
            parameters: null
        );

        // Create step with component
        $step = new Step(
            id: 1,
            organization_id: 1,
            flow_id: 1,
            step: 'welcome',
            type: 'interactive',
            step_type: StepType::INTERACTIVE,
            position: 1,
            next_step: 2,
            earlier_step: null,
            is_initial_step: true,
            is_ending_step: false,
            configuration: null,
            navigation_rules: null,
            timeout_seconds: null,
            json: null,
            input: null,
            created_at: null,
            updated_at: null,
            flow: null,
            component: $component,
            stepNavigations: null
        );

        // Create mock conversation and interaction
        $conversation = new Conversation(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: 1,
            phone_number_id: 1,
            current_step_id: 1,
            json: null,
            is_finished: false,
            created_at: null,
            updated_at: null,
            whatsapp_contact_name: 'Test User',
            whatsapp_profile_name: 'Test',
            whatsapp_metadata: [],
            client: null
        );

        $interaction = new Interaction(
            id: 1,
            organization_id: 1,
            user_id: null,
            client_id: null,
            flow_id: 1,
            step_id: 1,
            conversation_id: 1,
            message: '{"type":"button_reply","button_reply":{"id":"btn1","title":"Option 1"}}',
            answer: null,
            result: null,
            json: null,
            created_at: null,
            updated_at: null,
            whatsapp_message_id: 'msg123',
            whatsapp_message_type: 'interactive',
            whatsapp_raw_data: []
        );

        // Test that the processor can handle the step without throwing the "components" error
        $this->assertTrue($processor->canProcess($step));

        // Test processing (this should not throw the "Undefined property: components" error)
        $result = $processor->process($step, $interaction, $conversation);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('type', $result);
    }

    /** @test */
    public function it_handles_step_without_component()
    {
        $processor = app()->make(InteractiveStepProcessor::class);

        // Create step without component
        $step = new Step(
            id: 1,
            organization_id: 1,
            flow_id: 1,
            step: 'welcome',
            type: 'interactive',
            step_type: StepType::INTERACTIVE,
            position: 1,
            next_step: 2,
            earlier_step: null,
            is_initial_step: true,
            is_ending_step: false,
            configuration: null,
            navigation_rules: null,
            timeout_seconds: null,
            json: null,
            input: null,
            created_at: null,
            updated_at: null,
            flow: null,
            component: null, // No component
            stepNavigations: null
        );

        // This should not throw an error
        $this->assertTrue($processor->canProcess($step));
    }
}
