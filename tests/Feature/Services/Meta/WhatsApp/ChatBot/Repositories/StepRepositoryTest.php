<?php

namespace Tests\Feature\Services\Meta\WhatsApp\ChatBot\Repositories;

use Tests\TestCase;
use App\Repositories\StepRepository;
use App\Models\Step as StepModel;
use App\Models\Flow as FlowModel;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StepRepositoryTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_be_instantiated_via_app_make()
    {
        $repository = app()->make(StepRepository::class);
        $this->assertInstanceOf(StepRepository::class, $repository);
    }

    /** @test */
    public function it_can_find_step_by_identifier_in_flow()
    {
        // Create test data
        $organization = Organization::factory()->create();
        $flow = FlowModel::factory()->create(['organization_id' => $organization->id]);
        $step = StepModel::factory()->create([
            'organization_id' => $organization->id,
            'flow_id' => $flow->id,
            'step' => 'test_step_identifier'
        ]);

        $repository = app()->make(StepRepository::class);
        $result = $repository->findByIdentifierInFlow('test_step_identifier', $flow->id);

        $this->assertNotNull($result);
        $this->assertEquals($step->id, $result->id);
        $this->assertEquals('test_step_identifier', $result->step);
    }

    /** @test */
    public function it_returns_null_when_step_not_found()
    {
        $organization = Organization::factory()->create();
        $flow = FlowModel::factory()->create(['organization_id' => $organization->id]);

        $repository = app()->make(StepRepository::class);
        $result = $repository->findByIdentifierInFlow('nonexistent_step', $flow->id);

        $this->assertNull($result);
    }

    /** @test */
    public function it_can_find_step_by_id()
    {
        $organization = Organization::factory()->create();
        $flow = FlowModel::factory()->create(['organization_id' => $organization->id]);
        $step = StepModel::factory()->create([
            'organization_id' => $organization->id,
            'flow_id' => $flow->id
        ]);

        $repository = app()->make(StepRepository::class);
        $result = $repository->findById($step->id);

        $this->assertNotNull($result);
        $this->assertEquals($step->id, $result->id);
    }

    /** @test */
    public function it_can_get_initial_step_for_flow()
    {
        $organization = Organization::factory()->create();
        $flow = FlowModel::factory()->create(['organization_id' => $organization->id]);

        // Create initial step
        $initialStep = StepModel::factory()->create([
            'organization_id' => $organization->id,
            'flow_id' => $flow->id,
            'is_initial_step' => true,
            'position' => 1
        ]);

        // Create another step
        StepModel::factory()->create([
            'organization_id' => $organization->id,
            'flow_id' => $flow->id,
            'is_initial_step' => false,
            'position' => 2
        ]);

        $repository = app()->make(StepRepository::class);
        $result = $repository->getInitialStepForFlow($flow->id);

        $this->assertNotNull($result);
        $this->assertEquals($initialStep->id, $result->id);
        $this->assertTrue($result->is_initial_step);
    }

    /** @test */
    public function it_falls_back_to_first_step_when_no_initial_step_marked()
    {
        $organization = Organization::factory()->create();
        $flow = FlowModel::factory()->create(['organization_id' => $organization->id]);

        // Create steps without initial step marked
        $firstStep = StepModel::factory()->create([
            'organization_id' => $organization->id,
            'flow_id' => $flow->id,
            'is_initial_step' => false,
            'position' => 1
        ]);

        StepModel::factory()->create([
            'organization_id' => $organization->id,
            'flow_id' => $flow->id,
            'is_initial_step' => false,
            'position' => 2
        ]);

        $repository = app()->make(StepRepository::class);
        $result = $repository->getInitialStepForFlow($flow->id);

        $this->assertNotNull($result);
        $this->assertEquals($firstStep->id, $result->id);
    }
}
