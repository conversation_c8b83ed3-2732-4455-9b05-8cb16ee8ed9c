<?php

namespace Tests\Feature\Factories\ChatBot;

use Tests\TestCase;
use App\Factories\ChatBot\ButtonFactory;
use App\Domains\ChatBot\Button;

class ButtonFactoryJsonTest extends TestCase
{
    /** @test */
    public function it_can_be_instantiated_via_app_make()
    {
        $factory = app()->make(ButtonFactory::class);
        $this->assertInstanceOf(ButtonFactory::class, $factory);
    }

    /** @test */
    public function it_handles_callback_data_as_array_in_buildFromSaveFullButton()
    {
        $factory = app()->make(ButtonFactory::class);
        
        $buttonData = [
            'text' => 'Test Button',
            'type' => 'reply',
            'callback_data' => ['flavor' => 'Chocolate'] // Array
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        $this->assertInstanceOf(Button::class, $button);
        $this->assertEquals('{"flavor":"Chocolate"}', $button->callback_data);
        $this->assertIsArray($button->callback_data_array);
        $this->assertEquals('Chocolate', $button->callback_data_array['flavor']);
    }

    /** @test */
    public function it_handles_callback_data_as_valid_json_string_in_buildFromSaveFullButton()
    {
        $factory = app()->make(ButtonFactory::class);
        
        $buttonData = [
            'text' => 'Test Button',
            'type' => 'reply',
            'callback_data' => '{"flavor":"Frango"}' // Valid JSON string
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        $this->assertInstanceOf(Button::class, $button);
        $this->assertEquals('{"flavor":"Frango"}', $button->callback_data);
        $this->assertIsArray($button->callback_data_array);
        $this->assertEquals('Frango', $button->callback_data_array['flavor']);
    }

    /** @test */
    public function it_handles_callback_data_as_invalid_json_string_in_buildFromSaveFullButton()
    {
        $factory = app()->make(ButtonFactory::class);
        
        $buttonData = [
            'text' => 'Test Button',
            'type' => 'reply',
            'callback_data' => 'not a json string' // Invalid JSON string
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        $this->assertInstanceOf(Button::class, $button);
        // Should wrap the string in a JSON object
        $this->assertEquals('{"value":"not a json string"}', $button->callback_data);
        $this->assertIsArray($button->callback_data_array);
        $this->assertEquals('not a json string', $button->callback_data_array['value']);
    }

    /** @test */
    public function it_handles_callback_data_as_double_encoded_json_in_buildFromSaveFullButton()
    {
        $factory = app()->make(ButtonFactory::class);
        
        $buttonData = [
            'text' => 'Test Button',
            'type' => 'reply',
            'callback_data' => '"{\"flavor\":\"Chocolate\"}"' // Double encoded JSON
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        $this->assertInstanceOf(Button::class, $button);
        // Should use the string as-is since it's valid JSON (even if double encoded)
        $this->assertEquals('"{\"flavor\":\"Chocolate\"}"', $button->callback_data);
        // But callback_data_array should be null because it doesn't decode to an array
        $this->assertNull($button->callback_data_array);
    }

    /** @test */
    public function it_handles_empty_callback_data_in_buildFromSaveFullButton()
    {
        $factory = app()->make(ButtonFactory::class);
        
        $buttonData = [
            'text' => 'Test Button',
            'type' => 'reply'
            // No callback_data
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        $this->assertInstanceOf(Button::class, $button);
        $this->assertNull($button->callback_data);
        $this->assertNull($button->callback_data_array);
    }

    /** @test */
    public function it_handles_reply_fallback_in_buildFromSaveFullButton()
    {
        $factory = app()->make(ButtonFactory::class);
        
        $buttonData = [
            'text' => 'Test Button',
            'type' => 'reply',
            'reply' => ['action' => 'click'] // Using 'reply' instead of 'callback_data'
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        $this->assertInstanceOf(Button::class, $button);
        $this->assertEquals('{"action":"click"}', $button->callback_data);
        $this->assertIsArray($button->callback_data_array);
        $this->assertEquals('click', $button->callback_data_array['action']);
    }
}
