<?php

namespace Tests\Feature\Factories\ChatBot;

use Tests\TestCase;
use App\Factories\ChatBot\ButtonFactory;
use App\Domains\ChatBot\Button;

class ButtonFactoryRealWorldTest extends TestCase
{
    /** @test */
    public function it_handles_real_world_double_encoded_scenario()
    {
        $factory = app()->make(ButtonFactory::class);
        
        // This simulates the exact scenario you found in the logs
        $buttonData = [
            'text' => 'Chocolate',
            'type' => 'reply',
            'callback_data' => '"{\"flavor\":\"Chocolate\"}"' // Double encoded from logs
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        $this->assertInstanceOf(Button::class, $button);
        
        // The factory should detect this is valid JSON and use as-is
        $this->assertEquals('"{\"flavor\":\"Chocolate\"}"', $button->callback_data);
        
        // But the Button domain should handle it gracefully
        // Since it's valid JSON but decodes to a string (not array), callback_data_array should be null
        $this->assertNull($button->callback_data_array);
        
        // Let's verify what happens when we decode it manually
        $firstDecode = json_decode($button->callback_data, true);
        $this->assertEquals('{"flavor":"Chocolate"}', $firstDecode);
        
        $secondDecode = json_decode($firstDecode, true);
        $this->assertIsArray($secondDecode);
        $this->assertEquals('Chocolate', $secondDecode['flavor']);
    }

    /** @test */
    public function it_handles_another_real_world_scenario()
    {
        $factory = app()->make(ButtonFactory::class);
        
        // Another scenario from your logs
        $buttonData = [
            'text' => 'Frango',
            'type' => 'reply',
            'callback_data' => '"{\"flavor\":\"Frango\"}"' // Double encoded
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        $this->assertInstanceOf(Button::class, $button);
        $this->assertEquals('"{\"flavor\":\"Frango\"}"', $button->callback_data);
        $this->assertNull($button->callback_data_array);
    }

    /** @test */
    public function it_handles_properly_encoded_scenario()
    {
        $factory = app()->make(ButtonFactory::class);
        
        // This is how it should be
        $buttonData = [
            'text' => 'Chocolate',
            'type' => 'reply',
            'callback_data' => ['flavor' => 'Chocolate'] // Proper array
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        $this->assertInstanceOf(Button::class, $button);
        $this->assertEquals('{"flavor":"Chocolate"}', $button->callback_data);
        $this->assertIsArray($button->callback_data_array);
        $this->assertEquals('Chocolate', $button->callback_data_array['flavor']);
    }

    /** @test */
    public function it_handles_single_encoded_json_string()
    {
        $factory = app()->make(ButtonFactory::class);
        
        // Single encoded JSON string (correct)
        $buttonData = [
            'text' => 'Chocolate',
            'type' => 'reply',
            'callback_data' => '{"flavor":"Chocolate"}' // Single encoded
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        $this->assertInstanceOf(Button::class, $button);
        $this->assertEquals('{"flavor":"Chocolate"}', $button->callback_data);
        $this->assertIsArray($button->callback_data_array);
        $this->assertEquals('Chocolate', $button->callback_data_array['flavor']);
    }

    /** @test */
    public function it_demonstrates_the_problem_and_solution()
    {
        // BEFORE: This would cause the double encoding problem
        $problematicData = ['flavor' => 'Chocolate'];
        $firstEncode = json_encode($problematicData); // {"flavor":"Chocolate"}
        $secondEncode = json_encode($firstEncode);    // "{\"flavor\":\"Chocolate\"}"
        
        // This is what was happening in the old code
        $this->assertEquals('"{\"flavor\":\"Chocolate\"}"', $secondEncode);
        
        // AFTER: Our new validation prevents this
        $factory = app()->make(ButtonFactory::class);
        
        // Test with the double-encoded string
        $buttonData = [
            'text' => 'Test',
            'type' => 'reply',
            'callback_data' => $secondEncode // Double encoded
        ];
        
        $button = $factory->buildFromSaveFullButton($buttonData, null, 1, null);
        
        // The factory detects it's valid JSON and doesn't encode again
        $this->assertEquals($secondEncode, $button->callback_data);
        
        // The Button domain handles it gracefully (returns null for non-array JSON)
        $this->assertNull($button->callback_data_array);
        
        // Test with the properly encoded string
        $buttonData2 = [
            'text' => 'Test',
            'type' => 'reply',
            'callback_data' => $firstEncode // Single encoded
        ];
        
        $button2 = $factory->buildFromSaveFullButton($buttonData2, null, 1, null);
        
        // Works correctly
        $this->assertEquals($firstEncode, $button2->callback_data);
        $this->assertIsArray($button2->callback_data_array);
        $this->assertEquals('Chocolate', $button2->callback_data_array['flavor']);
    }
}
