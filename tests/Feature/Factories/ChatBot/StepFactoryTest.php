<?php

namespace Tests\Feature\Factories\ChatBot;

use Tests\TestCase;
use App\Factories\ChatBot\StepFactory;
use App\Models\Step as StepModel;
use App\Enums\StepType;
use App\Domains\ChatBot\Step;
use Mockery;

class StepFactoryTest extends TestCase
{
    /** @test */
    public function it_can_be_instantiated_via_app_make()
    {
        $factory = app()->make(StepFactory::class);
        $this->assertInstanceOf(StepFactory::class, $factory);
    }

    /** @test */
    public function it_handles_configuration_as_string_from_database()
    {
        // Create a mock step model with configuration as JSON string (simulating database storage)
        $stepModel = Mockery::mock(StepModel::class);
        $stepModel->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $stepModel->shouldReceive('getAttribute')->with('organization_id')->andReturn(1);
        $stepModel->shouldReceive('getAttribute')->with('flow_id')->andReturn(1);
        $stepModel->shouldReceive('getAttribute')->with('step')->andReturn('test_step');
        $stepModel->shouldReceive('getAttribute')->with('type')->andReturn('message');
        $stepModel->shouldReceive('getAttribute')->with('step_type')->andReturn(StepType::MESSAGE);
        $stepModel->shouldReceive('getAttribute')->with('position')->andReturn(1);
        $stepModel->shouldReceive('getAttribute')->with('next_step')->andReturn(null);
        $stepModel->shouldReceive('getAttribute')->with('earlier_step')->andReturn(null);
        $stepModel->shouldReceive('getAttribute')->with('is_initial_step')->andReturn(true);
        $stepModel->shouldReceive('getAttribute')->with('is_ending_step')->andReturn(false);
        $stepModel->shouldReceive('getAttribute')->with('configuration')->andReturn('{"text": "Hello World", "media_type": null}'); // JSON string
        $stepModel->shouldReceive('getAttribute')->with('navigation_rules')->andReturn('{"rules": []}'); // JSON string
        $stepModel->shouldReceive('getAttribute')->with('timeout_seconds')->andReturn(30);
        $stepModel->shouldReceive('getAttribute')->with('json')->andReturn(null);
        $stepModel->shouldReceive('getAttribute')->with('input')->andReturn(null);
        $stepModel->shouldReceive('getAttribute')->with('created_at')->andReturn(now());
        $stepModel->shouldReceive('getAttribute')->with('updated_at')->andReturn(now());
        $stepModel->shouldReceive('getAttribute')->with('flow')->andReturn(null);
        $stepModel->shouldReceive('getAttribute')->with('component')->andReturn(null);
        $stepModel->shouldReceive('getAttribute')->with('stepNavigations')->andReturn(null);

        // Use magic method access
        $stepModel->id = 1;
        $stepModel->organization_id = 1;
        $stepModel->flow_id = 1;
        $stepModel->step = 'test_step';
        $stepModel->type = 'message';
        $stepModel->step_type = StepType::MESSAGE;
        $stepModel->position = 1;
        $stepModel->next_step = null;
        $stepModel->earlier_step = null;
        $stepModel->is_initial_step = true;
        $stepModel->is_ending_step = false;
        $stepModel->configuration = '{"text": "Hello World", "media_type": null}'; // JSON string
        $stepModel->navigation_rules = '{"rules": []}'; // JSON string
        $stepModel->timeout_seconds = 30;
        $stepModel->json = null;
        $stepModel->input = null;
        $stepModel->created_at = now();
        $stepModel->updated_at = now();
        $stepModel->flow = null;
        $stepModel->component = null;
        $stepModel->stepNavigations = null;

        $factory = app()->make(StepFactory::class);

        // This should not throw an exception
        $step = $factory->buildFromModel($stepModel, false, false, false);

        $this->assertInstanceOf(Step::class, $step);
        $this->assertIsArray($step->configuration);
        $this->assertEquals('Hello World', $step->configuration['text']);
        $this->assertIsArray($step->navigation_rules);
        $this->assertEquals([], $step->navigation_rules['rules']);
    }

    /** @test */
    public function it_handles_null_configuration()
    {
        // Create a mock step model with null configuration
        $stepModel = Mockery::mock(StepModel::class);
        $stepModel->id = 1;
        $stepModel->organization_id = 1;
        $stepModel->flow_id = 1;
        $stepModel->step = 'test_step';
        $stepModel->type = 'message';
        $stepModel->step_type = StepType::MESSAGE;
        $stepModel->position = 1;
        $stepModel->next_step = null;
        $stepModel->earlier_step = null;
        $stepModel->is_initial_step = true;
        $stepModel->is_ending_step = false;
        $stepModel->configuration = null;
        $stepModel->navigation_rules = null;
        $stepModel->timeout_seconds = 30;
        $stepModel->json = null;
        $stepModel->input = null;
        $stepModel->created_at = now();
        $stepModel->updated_at = now();
        $stepModel->flow = null;
        $stepModel->component = null;
        $stepModel->stepNavigations = null;

        $factory = app()->make(StepFactory::class);

        // This should not throw an exception
        $step = $factory->buildFromModel($stepModel, false, false, false);

        $this->assertInstanceOf(Step::class, $step);
        $this->assertNull($step->configuration);
        $this->assertNull($step->navigation_rules);
    }

    /** @test */
    public function it_handles_invalid_json_configuration()
    {
        // Create a mock step model with invalid JSON configuration
        $stepModel = Mockery::mock(StepModel::class);
        $stepModel->id = 1;
        $stepModel->organization_id = 1;
        $stepModel->flow_id = 1;
        $stepModel->step = 'test_step';
        $stepModel->type = 'message';
        $stepModel->step_type = StepType::MESSAGE;
        $stepModel->position = 1;
        $stepModel->next_step = null;
        $stepModel->earlier_step = null;
        $stepModel->is_initial_step = true;
        $stepModel->is_ending_step = false;
        $stepModel->configuration = 'invalid json string'; // Invalid JSON
        $stepModel->navigation_rules = 'another invalid json'; // Invalid JSON
        $stepModel->timeout_seconds = 30;
        $stepModel->json = null;
        $stepModel->input = null;
        $stepModel->created_at = now();
        $stepModel->updated_at = now();
        $stepModel->flow = null;
        $stepModel->component = null;
        $stepModel->stepNavigations = null;

        $factory = app()->make(StepFactory::class);

        // This should not throw an exception and should handle invalid JSON gracefully
        $step = $factory->buildFromModel($stepModel, false, false, false);

        $this->assertInstanceOf(Step::class, $step);
        $this->assertNull($step->configuration); // Should be null for invalid JSON
        $this->assertNull($step->navigation_rules); // Should be null for invalid JSON
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
