<?php

namespace Tests\Feature\Factories\ChatBot;

use Tests\TestCase;
use App\Factories\ChatBot\ConversationFactory;
use App\Domains\ChatBot\Conversation;
use App\Models\Conversation as ConversationModel;
use App\Models\PhoneNumber as PhoneNumberModel;
use App\Models\Client as ClientModel;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ConversationFactoryTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_be_instantiated_via_app_make()
    {
        $factory = app()->make(ConversationFactory::class);
        $this->assertInstanceOf(ConversationFactory::class, $factory);
    }

    /** @test */
    public function it_builds_conversation_from_model_without_relationships()
    {
        $organization = Organization::factory()->create();
        $conversation = ConversationModel::factory()->create([
            'organization_id' => $organization->id,
            'is_finished' => false
        ]);

        $factory = app()->make(ConversationFactory::class);
        $domain = $factory->buildFromModel($conversation, false); // No relationships

        $this->assertInstanceOf(Conversation::class, $domain);
        $this->assertEquals($conversation->id, $domain->id);
        $this->assertEquals($conversation->organization_id, $domain->organization_id);
        $this->assertNull($domain->phone_number);
        $this->assertNull($domain->current_step);
    }

    /** @test */
    public function it_builds_conversation_with_phone_number_when_loaded()
    {
        $organization = Organization::factory()->create();
        $phoneNumber = PhoneNumberModel::factory()->create(['organization_id' => $organization->id]);
        
        $conversation = ConversationModel::factory()->create([
            'organization_id' => $organization->id,
            'phone_number_id' => $phoneNumber->id,
            'is_finished' => false
        ]);

        // Load phoneNumber relationship
        $conversation->load(['phoneNumber']);

        $factory = app()->make(ConversationFactory::class);
        $domain = $factory->buildFromModel($conversation, true); // With relationships

        $this->assertInstanceOf(Conversation::class, $domain);
        $this->assertEquals($conversation->phone_number_id, $domain->phone_number_id);
        
        // Check that phone_number is properly loaded
        $this->assertNotNull($domain->phone_number);
        $this->assertEquals($phoneNumber->id, $domain->phone_number->id);
    }

    /** @test */
    public function it_builds_conversation_with_current_step_when_loaded()
    {
        $organization = Organization::factory()->create();
        $flow = \App\Models\Flow::factory()->create(['organization_id' => $organization->id]);
        $step = \App\Models\Step::factory()->create([
            'organization_id' => $organization->id,
            'flow_id' => $flow->id,
            'step' => 'test_step'
        ]);
        
        $conversation = ConversationModel::factory()->create([
            'organization_id' => $organization->id,
            'current_step_id' => $step->id,
            'is_finished' => false
        ]);

        // Load currentStep relationship
        $conversation->load(['currentStep']);

        $factory = app()->make(ConversationFactory::class);
        $domain = $factory->buildFromModel($conversation, true); // With relationships

        $this->assertInstanceOf(Conversation::class, $domain);
        $this->assertEquals($conversation->current_step_id, $domain->current_step_id);
        
        // Check that current_step is properly loaded
        $this->assertNotNull($domain->current_step);
        $this->assertEquals($step->id, $domain->current_step->id);
        $this->assertEquals('test_step', $domain->current_step->step);
    }

    /** @test */
    public function it_builds_conversation_without_relationships_when_not_loaded()
    {
        $organization = Organization::factory()->create();
        $phoneNumber = PhoneNumberModel::factory()->create(['organization_id' => $organization->id]);
        
        $conversation = ConversationModel::factory()->create([
            'organization_id' => $organization->id,
            'phone_number_id' => $phoneNumber->id,
            'is_finished' => false
        ]);

        // Don't load relationships

        $factory = app()->make(ConversationFactory::class);
        $domain = $factory->buildFromModel($conversation, true); // With relationships flag, but not loaded

        $this->assertInstanceOf(Conversation::class, $domain);
        $this->assertEquals($conversation->phone_number_id, $domain->phone_number_id);
        
        // Check that phone_number is null when relationship is not loaded
        $this->assertNull($domain->phone_number);
        $this->assertNull($domain->current_step);
    }
}
