<?php

namespace Tests\Feature\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Log;

class CloseInactiveConversationWithLogsTest extends TestCase
{
    use RefreshDatabase;

    public function test_logs_are_created_when_processing_conversations(): void
    {
        // Count logs before running command
        $logsBefore = Log::count();

        // Run the command
        $this->artisan('chatbot:close-inactive-conversations --dry-run --limit=5')
             ->assertExitCode(0);

        // Count logs after running command
        $logsAfter = Log::count();

        // Should have created at least 2 logs (start and completion)
        $this->assertGreaterThanOrEqual($logsBefore + 2, $logsAfter);

        // Check that logs were created with correct 'from' field
        $this->assertDatabaseHas('logs', [
            'from' => 'CloseInactiveConversationsCommand',
            'message' => 'ChatBot inactive conversations closure cron started'
        ]);

        $this->assertDatabaseHas('logs', [
            'from' => 'CloseInactiveConversationsCommand',
            'message' => 'ChatBot inactive conversations closure cron completed'
        ]);
    }
}
